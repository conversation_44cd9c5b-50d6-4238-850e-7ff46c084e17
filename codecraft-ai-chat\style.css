body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #a8d5a2;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;
    padding: 20px;
    box-sizing: border-box;
}

.container {
    text-align: center;
    width: 100%;
    max-width: 600px;
}

h1 {
    font-size: 2.2rem;
    font-weight: 400;
    color: #2d4a2b;
    margin-bottom: 40px;
    letter-spacing: -0.5px;
}

.search-box {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-radius: 25px;
    padding: 12px 20px;
    margin: 0 auto 30px;
    width: 100%;
    max-width: 500px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: relative;
}

.left-section {
    display: flex;
    align-items: center;
    margin-right: 15px;
}

.tag {
    background: #6c757d;
    color: white;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 500;
    border: none;
    cursor: pointer;
}

.tag.active {
    background: #2d4a2b;
}

.search-box input {
    border: none;
    outline: none;
    flex: 1;
    font-size: 1rem;
    background: transparent;
    color: #333;
    padding: 8px 0;
}

.search-box input::placeholder {
    color: #999;
}

.right-icons {
    display: flex;
    align-items: center;
    gap: 15px;
}

.right-icons i {
    font-size: 1.1rem;
    color: #666;
    cursor: pointer;
    transition: color 0.2s;
}

.right-icons i:hover {
    color: #333;
}

.send-btn {
    border: none;
    background: #2d4a2b;
    color: white;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s;
}

.send-btn:hover {
    background: #1e3319;
}

.tags {
    display: flex;
    justify-content: center;
    gap: 12px;
    flex-wrap: wrap;
    margin-top: 20px;
}

.tags span {
    background: rgba(255, 255, 255, 0.3);
    color: #2d4a2b;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    cursor: pointer;
    border: 1px solid rgba(255, 255, 255, 0.5);
    transition: all 0.2s;
    font-weight: 500;
}

.tags span:hover {
    background: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 0 20px;
    }

    h1 {
        font-size: 1.8rem;
        margin-bottom: 30px;
    }

    .search-box {
        padding: 10px 16px;
    }

    .tags {
        gap: 8px;
    }

    .tags span {
        padding: 6px 12px;
        font-size: 0.85rem;
    }
}
