body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #a8d5a2;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;
}

.container {
    text-align: center;
    width: 100%;
    max-width: 600px;
}

h1 {
    font-size: 2.5rem;
    font-weight: 400;
    color: #2d4a2b;
    margin-bottom: 40px;
}

.search-box {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-radius: 25px;
    padding: 12px 20px;
    margin: 0 auto 30px;
    width: 500px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.left-section {
    display: flex;
    align-items: center;
    margin-right: 15px;
}

.tag {
    background: #6c757d;
    color: white;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 500;
}

.tag.active {
    background: #2d4a2b;
}

.search-box input {
    border: none;
    outline: none;
    flex: 1;
    font-size: 1rem;
    background: transparent;
    color: #333;
}

.search-box input::placeholder {
    color: #999;
}

.right-icons {
    display: flex;
    align-items: center;
    gap: 15px;
}

.right-icons i {
    font-size: 1.1rem;
    color: #666;
    cursor: pointer;
}

.send-btn {
    border: none;
    background: #2d4a2b;
    color: white;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.send-btn:hover {
    background: #1e3319;
}

.tags {
    display: flex;
    justify-content: center;
    gap: 12px;
    flex-wrap: wrap;
}

.tags span {
    background: rgba(255, 255, 255, 0.3);
    color: #2d4a2b;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    cursor: pointer;
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.tags span:hover {
    background: rgba(255, 255, 255, 0.5);
}
